package main

import (
	"bufio"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
	"github.com/kryptogo/kg-solana-data/data-writer/token_balance"
)

func readWalletsFromFile(filePath string) ([]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open wallet file: %v", err)
	}
	defer file.Close()

	var wallets []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		wallet := strings.TrimSpace(scanner.Text())
		if wallet != "" {
			wallets = append(wallets, wallet)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading wallet file: %v", err)
	}

	return wallets, nil
}

func main() {
	// Parse command line arguments
	walletsStr := flag.String("wallets", "", "Comma-separated list of wallet addresses")
	walletsFile := flag.String("wallets-file", "", "Path to file containing wallet addresses (one per line)")
	tokenMintStr := flag.String("token-mint", "", "Token mint address")
	fromSlot := flag.Uint64("from-slot", 0, "Starting slot number")
	outputFile := flag.String("output", "balance_points.json", "Output JSON file path")
	flag.Parse()

	if (*walletsStr == "" && *walletsFile == "") || *tokenMintStr == "" || *fromSlot == 0 {
		log.Fatal("either wallets or wallets-file, token-mint, and from-slot are required")
	}

	// Initialize logger
	ctx := context.Background()
	if err := logger.Initialize(ctx, "token-balance"); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Close()

	// Initialize database
	if err := db.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Get wallet addresses
	var walletStrs []string
	var err error
	if *walletsFile != "" {
		walletStrs, err = readWalletsFromFile(*walletsFile)
		if err != nil {
			log.Fatalf("Failed to read wallets from file: %v", err)
		}
	} else {
		walletStrs = strings.Split(*walletsStr, ",")
	}

	// Create configuration
	config := token_balance.TokenBalanceConfig{
		FromSlot:         *fromSlot,
		RPCURL:           "", // Will use environment variable or default
		CommitmentLevel:  "", // Will use default (Finalized)
		DefaultRPCURL:    "https://api.mainnet-beta.solana.com",
		ContinueOnErrors: false, // Fail on errors for CLI tool
	}

	// Call the reusable function
	libResult, err := token_balance.CalculateTokenBalanceHistory(ctx, *tokenMintStr, walletStrs, config)
	if err != nil {
		log.Fatalf("Failed to calculate token balance history: %v", err)
	}

	log.Printf("totalCurrentBalance: %d, numIntervals: %d", libResult.TotalCurrentBalance, libResult.NumIntervals)

	// Write to JSON file
	file, err := os.Create(*outputFile)
	if err != nil {
		log.Fatalf("Failed to create output file: %v", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(libResult.BalancePoints); err != nil {
		log.Fatalf("Failed to encode balance points: %v", err)
	}
	log.Printf("Successfully wrote balance points to %s", *outputFile)
}
